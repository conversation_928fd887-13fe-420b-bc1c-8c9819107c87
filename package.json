{"name": "expense-tracker", "version": "1.0.0", "private": true, "scripts": {"start": "NODE_ENV=production node server.js", "dev": "NODE_ENV=development nodemon server.js", "local": "NODE_ENV=local nodemon server.js"}, "type": "module", "dependencies": {"bcryptjs": "^3.0.3", "cookie-parser": "^1.4.4", "debug": "^2.6.9", "dotenv": "^17.2.3", "express": "^5.1.0", "http-errors": "^1.6.3", "jsonwebtoken": "^9.0.2", "mongodb": "^7.0.0", "mongoose": "^8.19.3", "morgan": "^1.10.1", "zod": "^4.1.13"}, "devDependencies": {"nodemon": "^3.1.11"}}
import Group from "../models/group.model.js";
import GroupExpense from "../models/groupExpense.model.js";
import mongoose from 'mongoose'; // Required for checking ObjectId validity

export const createGroup = async (req, res, next) => {
    try {
        // Destructure fields from the request body
        // Note: The schema uses 'baseCurrency' and has timestamps (created with 'Date')
        const { name, members, baseCurrency, status } = req.body;

        // 1. Basic Presence Check (Required Fields)
        if (!name || !members || !baseCurrency) {
            return res.status(400).json({ message: "Fields 'name', 'members', and 'baseCurrency' are required." });
        }

        // 2. Data Type and Content Validation

        // Name Validation
        if (typeof name !== 'string' || name.trim().length === 0) {
            return res.status(400).json({ message: "Group name must be a non-empty string." });
        }

        // Members Validation (Array and Mongoose ObjectId Check)
        if (!Array.isArray(members) || members.length < 1) {
            return res.status(400).json({ message: "Members must be an array containing at least one user ID." });
        }

        // Check if all members are valid MongoDB ObjectIds
        const invalidMembers = members.filter(memberId =>
            !mongoose.Types.ObjectId.isValid(memberId)
        );

        if (invalidMembers.length > 0) {
            return res.status(400).json({ message: "One or more member IDs provided are invalid." });
        }

        // BaseCurrency Validation (Schema Default/Type)
        // Schema default is "INR", but if client sends it, validate it's a string.
        if (typeof baseCurrency !== 'string' || baseCurrency.length === 0) {
            return res.status(400).json({ message: "Base currency must be a non-empty string (e.g., USD, EUR, INR)." });
        }

        // Status Validation (Schema Enum Check)
        // Only validate if a status is provided, otherwise let the schema default handle it.
        if (status) {
            const validStatuses = ["active", "inactive"];
            if (!validStatuses.includes(status.toLowerCase())) {
                return res.status(400).json({
                    message: `Invalid status value. Must be one of: ${validStatuses.join(', ')}`
                });
            }
        }

        // --- End of Request Body Validation ---

        // 3. Database Interaction
        const group = await Group.create({
            name: name.trim(), // Trim whitespace from name
            members,
            baseCurrency: baseCurrency.toUpperCase(), // Store currency standardized
            status: status ? status.toLowerCase() : undefined, // Use provided status or let schema default apply
            user: req.user._id // Assuming req.user is populated by a prior authentication middleware
        });

        if (!group) {
            // This case should ideally be caught by a 500 handler, but checking is safer
            return res.status(500).json({ message: "Failed to create group in the database." });
        }

        // 4. Success Response
        return res.status(201).json({
            message: "Group created successfully",

        });

    } catch (error) {
        // Handle Mongoose/Database errors (e.g., schema validation errors from `Group.create`)
        if (error.name === 'ValidationError') {
            return res.status(400).json({ message: error.message });
        }

        // Pass other errors (like connection errors) to the error handler middleware
        next(error);
    }
}
// Define the valid split methods for easy reference
const VALID_SPLIT_METHODS = ["EQUAL", "PERCENTAGE", "CUSTOM"];

export const addGroupExpense = async (req, res, next) => {

    try {
        const { group, description, amount, payer, date, splitMethod, splitDetails } = req.body;

        // --- 1. Basic Presence and Format Checks (400 Bad Request) ---

        // Check required fields based on the schema
        if (!group || !description || !amount || !payer || !splitMethod || !splitDetails) {
            return res.status(400).json({ message: "Fields 'group', 'description', 'amount', 'payer', 'splitMethod', and 'splitDetails' are required." });
        }

        // Validate IDs (group and payer)
        if (!mongoose.Types.ObjectId.isValid(group) || !mongoose.Types.ObjectId.isValid(payer)) {
            return res.status(400).json({ message: "Invalid ID format provided for group or payer." });
        }

        // Validate Amount
        if (typeof amount !== 'number' || amount <= 0) {
            return res.status(400).json({ message: "Amount must be a positive number." });
        }

        // Validate Split Method (Enum check)
        const method = splitMethod.toUpperCase();
        if (!VALID_SPLIT_METHODS.includes(method)) {
            return res.status(400).json({ message: `Invalid splitMethod. Must be one of: ${VALID_SPLIT_METHODS.join(', ')}.` });
        }

        // Validate Split Details (Must be an array)
        if (!Array.isArray(splitDetails) || splitDetails.length === 0) {
            return res.status(400).json({ message: "splitDetails must be a non-empty array." });
        }

        // --- 2. Split Method Specific Validation (422 Unprocessable Entity) ---

        // Calculate totals for validation
        let totalAmount = 0;
        let totalPercentage = 0;
        const memberIds = new Set();
        let validationError = null;

        for (const detail of splitDetails) {
            // Check for valid User ID in splitDetails
            if (!detail.user || !mongoose.Types.ObjectId.isValid(detail.user)) {
                validationError = "All splitDetails must contain a valid 'user' ID.";
                break;
            }

            // Check for duplicate users in splitDetails
            if (memberIds.has(detail.user.toString())) {
                validationError = "Duplicate user found in splitDetails.";
                break;
            }
            memberIds.add(detail.user.toString());

            if (method === "EQUAL") {
                // For EQUAL split, 'amount' and 'percentage' fields must not be provided, or they are ignored.
                // We mainly check the array structure here.
            } else if (method === "CUSTOM") {
                // Must have a positive amount, and not have a percentage
                if (typeof detail.amount !== 'number' || detail.amount <= 0) {
                    validationError = "For 'CUSTOM' split, every detail must have a positive 'amount'.";
                    break;
                }
                if (detail.percentage !== undefined) {
                    validationError = "For 'CUSTOM' split, 'percentage' should not be present.";
                    break;
                }
                totalAmount += detail.amount;

            } else if (method === "PERCENTAGE") {
                // Must have a positive percentage, and not have an amount
                if (typeof detail.percentage !== 'number' || detail.percentage <= 0) {
                    validationError = "For 'PERCENTAGE' split, every detail must have a positive 'percentage'.";
                    break;
                }
                if (detail.amount !== undefined) {
                    validationError = "For 'PERCENTAGE' split, 'amount' should not be present.";
                    break;
                }
                totalPercentage += detail.percentage;
            }
        }

        if (validationError) {
            return res.status(422).json({ message: validationError });
        }

        // Check totals against the expense amount
        const expenseAmount = parseFloat(amount.toFixed(2)); // Use floating point comparison caution

        if (method === "PERCENTAGE" && Math.round(totalPercentage) !== 100) {
            return res.status(422).json({ message: `For PERCENTAGE split, total percentage must equal 100%. Found: ${totalPercentage}%` });
        }

        if (method === "CUSTOM" && parseFloat(totalAmount.toFixed(2)) !== expenseAmount) {
            return res.status(422).json({ message: `For CUSTOM split, the sum of all split amounts ($${totalAmount.toFixed(2)}) must equal the total expense amount ($${expenseAmount}).` });
        }

        // --- 3. Database Interaction ---

        // Final object creation payload
        const creationPayload = {
            group,
            description,
            amount,
            payer,
            // Only include 'date' if provided, otherwise the schema default Date.now will apply
            date: date ? new Date(date) : undefined,
            splitMethod: method, // Use the uppercased value
            splitDetails
        };

        const groupExpense = await GroupExpense.create(creationPayload);

        // This check is now mostly redundant due to the extensive validation above,
        // but can catch unexpected database/schema issues.
        if (!groupExpense) {
            return res.status(500).json({ message: "Failed to create group expense in the database." });
        }

        // 4. Success Response
        return res.status(201).json({
            message: "Group expense recorded successfully",
            expense: groupExpense
        });

    } catch (error) {
        // Handle Mongoose/Database errors (e.g., schema validation errors)
        if (error.name === 'ValidationError') {
            return res.status(400).json({ message: error.message });
        }

        // Pass other errors (like connection or server-side errors) to the error handler middleware
        next(error);
    }
}
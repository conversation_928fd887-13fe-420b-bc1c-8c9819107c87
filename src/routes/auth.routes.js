import express from "express";
import User from "../models/user.model.js";
import { registerSchema ,loginSchema} from "../validators/auth.validator.js";
import { validate } from "../middleware/validate.middleware.js";
import { register , login } from "../controllers/auth.controller.js";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";

const router = express.Router();

router.post("/register", validate(registerSchema), register);

router.post('/login', async (req, res, next) => {
   
});

export default router;
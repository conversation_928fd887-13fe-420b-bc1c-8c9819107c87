import mongoose from "mongoose";

const e164Regex = /^\+\d{7,15}$/;

const userSchema = new mongoose.Schema(
    {
        name: { type: String, required: true },

        email: { type: String, required: true, unique: true },

        password: { type: String, required: true },

        phoneNumber: {
            countryCode: { type: String, required: true },
            number: {
                type: String,
                required: true,
                match: [e164Regex, "Phone number must be E.164 format"]
            }
        }
    },
    { timestamps: true }
);

const User = mongoose.model("User", userSchema);

export default User;

import mongoose from "mongoose";

const groupExpenseSchema = new mongoose.Schema({

    group: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Group",
        required: true,
    },
    description: {
        type: String,
        required: true,
    },
    amount: {
        type: Number,
        required: true,
    },
    payer: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    date: {
        type: Date,
        default: Date.now,
    },
    splitMethod: {
        type: String,
        enum: ["EQUAL", "PERCENTAGE", "CUSTOM"],
        default: "EQUAL",
        required: true,
    },
    splitDetails: [
        {
            user: {
                type: mongoose.Schema.Types.ObjectId,

                ref: "User",
                required: true,
            },
            amount: Number,
            percentage: Number,
        }
    ],

},
    {
        timestamps: true,
        toJSON: {
            virtuals: true,
            transform: function (doc, ret) {
                ret.id = ret._id;   // add new field "id"
                delete ret._id;     // remove _id
                delete ret.__v;     // remove version key
            }
        }
    });

const GroupExpense = mongoose.model("GroupExpense", groupExpenseSchema);

export default GroupExpense;
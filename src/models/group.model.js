import mongoose from 'mongoose';

const groupSchema = new mongoose.Schema({
    name: {
        type: String,
        require: true,
    },
    members: [
        {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
            required: true
        }

    ],
    baseCurrency: {
        type: String,
        default: "INR",
    },
    status: {
        type: String,
        enum: ["active", "inactive"],
        default: "active",
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true,
        transform: function (doc, ret) {
            ret.id = ret._id;   // add new field "id"
            delete ret._id;     // remove _id
            delete ret.__v;     // remove version key
        },
    },

},);

const Group = mongoose.model('Group', groupSchema);

export default Group;